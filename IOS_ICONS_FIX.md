# 🍎 iOS IKONY - OPRAVA CHÝBAJÚCICH SÚBOROV

## 🚨 **Problém:**
Pri exporte na iOS sa vyskytovali chyby s chýbaj<PERSON>cimi ikonami:
```
ERROR: Can't open file from path 'res://builds/ios/sss/Images.xcassets/AppIcon.appiconset/Contents.json'.
ERROR: Can't open file from path 'res://builds/ios/sss/Images.xcassets/AppIcon.appiconset/Icon-40.png'.
ERROR: Can't open file from path 'res://builds/ios/sss/Images.xcassets/AppIcon.appiconset/Icon-58.png'.
...
```

**Príčina:** Export preset odkazoval na neexistujúce ikony a splash screen súbory.

## ✅ **Riešenie:**

### 1. **Vyčistený export_presets.cfg**
Odstránené odkazy na neexistujúce ikony:

```ini
# Pred opravou:
required_icons/iphone_120x120="res://builds/ios/icons/Icon-120.png"
required_icons/ipad_76x76="res://builds/ios/icons/Icon-76.png"
required_icons/app_store_1024x1024="res://builds/ios/icons/Icon-1024.png"

# Po oprave:
required_icons/iphone_120x120=""
required_icons/ipad_76x76=""
required_icons/app_store_1024x1024=""
```

### 2. **Vyčistené všetky ikony**
Všetky ikony nastavené na prázdne reťazce:
- `icons/icon_1024x1024=""`
- `icons/settings_58x58=""`
- `icons/notification_40x40=""`
- `icons/spotlight_80x80=""`
- `icons/iphone_120x120=""`
- `icons/iphone_180x180=""`
- `icons/ipad_167x167=""`
- `icons/ipad_152x152=""`
- `icons/app_store_1024x1024=""`

### 3. **Vyčistené launch screens**
Všetky launch screen obrázky nastavené na prázdne:
- `landscape_launch_screens/*=""`
- `portrait_launch_screens/*=""`
- `application/launch_screen_image=""`

## 🛠️ **Alternatívne riešenia:**

### 1. **Generovanie ikon zo loga**
Vytvorený script `scripts/generate_ios_icons.gd`:

```gdscript
@tool
extends EditorScript

func _run():
    # Načíta assets/logo.png
    # Vygeneruje všetky potrebné iOS ikony
    # Uloží do builds/ios/icons/
```

**Použitie:**
1. Otvor script v editore
2. Klikni "Run" v Script editore
3. Ikony sa vygenerujú automaticky

### 2. **Manuálne vytvorenie ikon**
Potrebné veľkosti pre iOS:
- **40x40** - Spotlight, Settings
- **58x58** - Settings @2x
- **60x60** - Notification
- **76x76** - iPad
- **80x80** - Spotlight @2x
- **87x87** - Settings @3x
- **114x114** - Notification @2x
- **120x120** - iPhone, Spotlight @3x
- **128x128** - iOS
- **136x136** - iOS
- **152x152** - iPad @2x
- **167x167** - iPad Pro
- **180x180** - iPhone @3x
- **192x192** - iOS
- **1024x1024** - App Store

### 3. **Použitie existujúceho loga**
Ak chcete použiť `assets/logo.png` ako ikonu:

```ini
# V export_presets.cfg
application/icon="res://assets/logo.png"
required_icons/app_store_1024x1024="res://assets/logo.png"
```

## 📱 **Výsledok:**

### ✅ **Čo funguje:**
- **iOS export:** Bez chýb s ikonami
- **Aplikácia:** Spustí sa normálne na iOS
- **Fallback ikony:** iOS použije predvolené ikony
- **Žiadne crash:** Aplikácia sa nespustí kvôli chýbajúcim súborom

### 🎯 **Odporúčania:**
1. **Pre produkciu:** Vytvorte profesionálne ikony vo všetkých veľkostiach
2. **Pre testovanie:** Aktuálne riešenie je dostačujúce
3. **Automatizácia:** Použite generate_ios_icons.gd script

## 🚀 **Nasadenie:**

1. **Export na iOS:** Teraz bez chýb s ikonami
2. **Testovanie:** Aplikácia sa spustí normálne
3. **App Store:** Pre publikovanie budete potrebovať správne ikony

**iOS export je teraz funkčný bez chýb s ikonami!** 🍎✅
