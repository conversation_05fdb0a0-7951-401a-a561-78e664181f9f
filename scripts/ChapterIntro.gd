extends Control

@onready var video_player: VideoStreamPlayer = $VideoPlayer
@onready var chapter_title: Label = $TextContainer/ChapterTitle
@onready var chapter_description: Label = $TextContainer/ChapterDescription
@onready var fade_overlay: ColorRect = $FadeOverlay
@onready var audio_player: AudioStreamPlayer = $AudioPlayer
@onready var background_image: TextureRect = $BackgroundImage

var current_chapter: int = 1

# Background images pre kapitoly
var chapter_backgrounds = {
	1: "res://assets/Obrázky/Kapitola_1/1.png",  # Kapitola 1 má iný priečinok
	2: "res://assets/pozadia/Kapitola_2/1.png",
	3: "res://assets/pozadia/Kapitola_3/1.png",
	4: "res://assets/pozadia/Kapitola_4/3.png",  # Kapitola 4 používa 3.png ako počiatočné
	5: "res://assets/pozadia/Kapitola_5/1.png",
	6: "res://assets/pozadia/Kapitola_6/1.png",
	7: "res://assets/pozadia/Kapitola_7/1.png"
}

# Texty pre kapitoly - presne podľa zadania
var chapter_data = {
	1: {
		"title": "KAPITOLA 1: CESTA NA ZÁMOK",
		"description": "Kočiar ťa nesie do srdca Karpát – k záhade, ktorá nedá Van Helsingovi spať.",
		"video": "res://assets/kapitoly_videa/1.ogv"
	},
	2: {
		"title": "KAPITOLA 2: BRÁNA ZÁMKU",
		"description": "Brána zámku sa otvára len tomu, kto pozná odpovede Rádu.",
		"video": "res://assets/kapitoly_videa/2.ogv"
	},
	3: {
		"title": "KAPITOLA 3: PÁTRANIE V ZÁMKU",
		"description": "Zmiznutie mentora ťa vedie hlbšie – do knižníc, denníkov a zakázaných spisov.",
		"video": "res://assets/kapitoly_videa/3.ogv"
	},
	4: {
		"title": "KAPITOLA 4: TAJNÉ KRÍDLO",
		"description": "Staré múry ukrývajú pasce, elixír i kľúč k Isabelleinej hrobke.",
		"video": "res://assets/kapitoly_videa/4.ogv"
	},
	5: {
		"title": "KAPITOLA 5: KRYPTY",
		"description": "Katakomby dýchajú smrťou – a v ich tieni číha minulosť rodu Báthoryovcov.",
		"video": "res://assets/kapitoly_videa/5.ogv"
	},
	6: {
		"title": "KAPITOLA 6: KONFRONTÁCIA",
		"description": "Grófka sa prebúdza… a ty si poslednou nádejou, že zlo znova neuspeje.",
		"video": "res://assets/kapitoly_videa/6.ogv"
	},
	7: {
		"title": "KAPITOLA 7: ZÁCHRANA MENTORA",
		"description": "Van Helsing žije – no jeho čas sa kráti. Boj ešte nie je celkom dohraný.",
		"video": "res://assets/kapitoly_videa/7.ogv"
	}
}

func _setup_mobile_layout():
	"""Nastaví responzívne rozloženie pre mobilné zariadenia"""
	var screen_size = get_viewport().get_visible_rect().size
	var screen_width = screen_size.x
	var screen_height = screen_size.y

	# Získaj TextContainer
	var text_container = $TextContainer

	if screen_width <= 480:  # Mobilné zariadenia (360-480px)
		# Užší kontajner s väčšími okrajmi pre lepšie zalamovanie
		text_container.offset_left = -260.0
		text_container.offset_right = 260.0
		text_container.offset_top = -200.0
		text_container.offset_bottom = 200.0

		# Menší spacer medzi titulkom a popisom
		var spacer = $TextContainer/Spacer
		spacer.custom_minimum_size = Vector2(0, 15)

	elif screen_width <= 768:  # Tablety
		# Stredný kontajner
		text_container.offset_left = -300.0
		text_container.offset_right = 300.0
		text_container.offset_top = -180.0
		text_container.offset_bottom = 180.0

		var spacer = $TextContainer/Spacer
		spacer.custom_minimum_size = Vector2(0, 20)
	else:  # Desktop
		# Pôvodné rozmery pre desktop
		text_container.offset_left = -300.0
		text_container.offset_right = 300.0
		text_container.offset_top = -150.0
		text_container.offset_bottom = 150.0

	# Pre všetky zariadenia - zabezpeč správne zalamovanie textu
	chapter_title.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	chapter_description.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART

	# Nastaví lepšie zalamovanie pre dlhšie texty
	chapter_title.clip_contents = true
	chapter_description.clip_contents = true

func _ready():
	# Okamžite zastav main menu hudbu pri spustení ChapterIntro
	if AudioManager:
		AudioManager.stop_main_menu_music_immediately()

	# Nastav responzívne rozloženie pre mobilné zariadenia
	_setup_mobile_layout()

	# Aplikuj správne fonty
	_apply_fonts()

	# Skryj text na začiatku a nastav fade overlay
	chapter_title.modulate.a = 0.0
	chapter_description.modulate.a = 0.0
	chapter_description.text = ""

	# Zabezpeč, že fade overlay začína úplne čierny
	fade_overlay.color.a = 1.0

	# Nastav background image pre aktuálnu kapitolu (skrytý na začiatku)
	if background_image:
		background_image.modulate.a = 0.0

	# Spustí intro pre aktuálnu kapitolu z GameManager
	if GameManager:
		start_chapter_intro(GameManager.current_chapter)
	else:
		print("❌ GameManager nedostupný, spúšťam kapitolu 1")
		start_chapter_intro(1)

func _apply_fonts():
	"""Aplikuje správne fonty podľa dizajnu hry s mobilnou optimalizáciou"""
	# Získaj veľkosť obrazovky pre responzívne fonty
	var screen_size = get_viewport().get_visible_rect().size
	var screen_width = screen_size.x

	# Titulok kapitoly - Cinzel, zlatá farba, responzívna veľkosť
	var title_size = 48  # Mierne zmenšené z 52
	if screen_width <= 480:  # Mobilné zariadenia
		title_size = 32  # Zmenšené z 36 pre lepšie zalamovanie
	elif screen_width <= 768:  # Tablety
		title_size = 40  # Zmenšené z 44

	FontLoader.apply_font_style(chapter_title, "chapter_title", title_size)

	# Popis kapitoly - Cormorant, krémová farba, responzívna veľkosť
	var desc_size = 26  # Mierne zmenšené z 28
	if screen_width <= 480:  # Mobilné zariadenia
		desc_size = 20  # Zmenšené z 22 pre lepšie zalamovanie
	elif screen_width <= 768:  # Tablety
		desc_size = 23  # Zmenšené z 25

	FontLoader.apply_font_style(chapter_description, "character_dialogue", desc_size)

func start_chapter_intro(chapter_number: int):
	"""Spustí intro pre danú kapitolu"""
	current_chapter = chapter_number
	print("🎬 Spúšťam intro pre kapitolu ", chapter_number)

	if not chapter_data.has(chapter_number):
		print("❌ Chyba: Neexistujúce dáta pre kapitolu ", chapter_number)
		_finish_intro()
		return

	var data = chapter_data[chapter_number]

	# Nastaví titulok
	chapter_title.text = data.title

	# Načíta background image pre kapitolu
	_load_background_image(chapter_number)

	# Načíta a spustí audio pre úvodné video
	_load_and_play_intro_audio(chapter_number)

	# Načíta a spustí video
	var video_stream = load(data.video)
	if video_stream:
		video_player.stream = video_stream
		video_player.play()

		# Pripojí signál pre koniec videa
		if not video_player.finished.is_connected(_on_video_finished):
			video_player.finished.connect(_on_video_finished)

		print("✅ Video načítané: ", data.video)

		# Pre kapitolu 1 - prehrať blesk sekundu po spustení videa
		if chapter_number == 1:
			_schedule_lightning_sound()
	else:
		print("❌ Chyba načítania videa: ", data.video)
		_finish_intro()
		return

	# Spustí animáciu
	_start_intro_animation(data.description)

func _start_intro_animation(description_text: String):
	"""Spustí animáciu intro s plynulými fade efektmi"""
	# Nastaví text popisu
	chapter_description.text = description_text

	# Vytvor hlavný tween pre sekvenčné animácie
	var main_tween = create_tween()
	main_tween.set_ease(Tween.EASE_IN_OUT)
	main_tween.set_trans(Tween.TRANS_CUBIC)

	# 1. Fade in z čiernej (1.5s) - pomalší, plynulejší prechod
	main_tween.tween_property(fade_overlay, "color:a", 0.0, 1.5)

	# 2. Pauza pred zobrazením titulku
	main_tween.tween_interval(0.8)

	# 3. Zobraz titulok s plynulým fade in (1.2s)
	main_tween.tween_property(chapter_title, "modulate:a", 1.0, 1.2)

	# 4. Kratšia pauza pred popisom
	main_tween.tween_interval(0.5)

	# 5. Zobraz popis s plynulým fade in (1.0s)
	main_tween.tween_property(chapter_description, "modulate:a", 1.0, 1.0)



func _on_video_finished():
	"""Callback po dokončení videa - počká ešte 5 sekúnd"""
	print("🎬 Video dokončené, čakám ešte 5 sekúnd")

	# Skryje video a zobrazí background image
	video_player.visible = false
	if background_image:
		background_image.modulate.a = 1.0

	# Počká 5 sekúnd pred dokončením intro
	await get_tree().create_timer(5.0).timeout

	print("🎬 5 sekúnd uplynulo, končím intro")
	_finish_intro()

func _load_background_image(chapter_number: int):
	"""Načíta background image pre kapitolu"""
	if not chapter_backgrounds.has(chapter_number):
		print("❌ Chyba: Neexistuje background pre kapitolu ", chapter_number)
		return

	var bg_path = chapter_backgrounds[chapter_number]
	print("🖼️ Načítavam background: ", bg_path)

	var bg_texture = load(bg_path)
	if bg_texture and background_image:
		background_image.texture = bg_texture
		print("✅ Background načítaný: ", bg_path)
	else:
		print("❌ Chyba načítania background: ", bg_path)

func _load_and_play_intro_audio(chapter_number: int):
	"""Načíta a spustí audio pre úvodné video kapitoly"""
	var audio_path = "res://audio/kapitoly_uvody_zvuky/" + str(chapter_number) + ".mp3"
	print("🎵 Načítavam intro audio: ", audio_path)

	var audio_stream = load(audio_path)
	if audio_stream:
		audio_player.stream = audio_stream
		audio_player.volume_db = -5.0  # Mierne znížená hlasitosť
		audio_player.play()
		print("✅ Intro audio spustené: ", audio_path)
	else:
		print("❌ Chyba načítania intro audia: ", audio_path)

func _schedule_lightning_sound():
	"""Naplánuje prehranie blesku sekundu po spustení videa"""
	print("⚡ Plánovanie blesku pre kapitolu 1 za 1 sekundu")
	await get_tree().create_timer(1.0).timeout

	if AudioManager:
		AudioManager.play_lightning_sound()
		print("⚡ Blesk prehrávam!")

func _finish_intro():
	"""Dokončí intro a prejde na kapitolu s plynulým fade out"""
	print("🎬 Dokončujem intro pre kapitolu ", current_chapter)

	# Vytvor plynulý fade out efekt
	var fade_tween = create_tween()
	fade_tween.set_ease(Tween.EASE_IN_OUT)
	fade_tween.set_trans(Tween.TRANS_CUBIC)

	# Najprv fade out text (0.8s) - pomalšie pre plynulejší efekt
	fade_tween.parallel().tween_property(chapter_title, "modulate:a", 0.0, 0.8)
	fade_tween.parallel().tween_property(chapter_description, "modulate:a", 0.0, 0.8)

	# Súčasne fade out intro audio (1.0s)
	if audio_player.playing:
		fade_tween.parallel().tween_property(audio_player, "volume_db", -30.0, 1.0)

	# Potom fade out do čiernej (1.2s) s miernym oneskorením
	fade_tween.tween_interval(0.3)
	fade_tween.tween_property(fade_overlay, "color:a", 1.0, 1.2)

	await fade_tween.finished

	# Zastaví intro audio
	if audio_player.playing:
		audio_player.stop()

	# Prejdi na kapitolu s crossfade na kapitolu hudbu
	_load_chapter()

func _load_chapter():
	"""Načíta príslušnú kapitolu"""
	print("📖 Načítavam kapitolu ", current_chapter)

	# Nastaví GameManager
	if GameManager:
		GameManager.current_chapter = current_chapter
		GameManager.story_phase = 0

	# Spustí hudbu pre kapitolu s crossfade efektom
	_start_chapter_music()

	# Načíta scénu kapitoly
	var chapter_scene = "res://scenes/Chapter" + str(current_chapter) + ".tscn"

	if ResourceLoader.exists(chapter_scene):
		get_tree().change_scene_to_file(chapter_scene)
	else:
		print("❌ Chyba: Scéna kapitoly neexistuje: ", chapter_scene)
		# Fallback na generickú Chapter scénu
		get_tree().change_scene_to_file("res://scenes/Chapter.tscn")

func _start_chapter_music():
	"""Spustí hudbu pre kapitolu s crossfade efektom"""
	if not AudioManager:
		print("❌ AudioManager nedostupný")
		return

	print("🎵 Spúšťam hudbu pre kapitolu ", current_chapter)

	# Spustí príslušnú hudbu pre kapitolu
	match current_chapter:
		1:
			AudioManager.start_chapter_1()
		2:
			AudioManager.start_chapter_2()
		3:
			AudioManager.start_chapter_3()
		4:
			AudioManager.start_chapter_4()
		5:
			AudioManager.start_chapter_5()
		6:
			AudioManager.start_chapter_6()
		7:
			AudioManager.start_chapter_7()
		_:
			print("⚠️ Neznáma kapitola, používam library_secrets")
			AudioManager.play_music("library_secrets")

func _input(event):
	"""Umožní preskočiť intro stlačením klávesy"""
	if event.is_pressed() and (event is InputEventKey or event is InputEventMouseButton):
		print("⏭️ Preskakujem intro")
		# Zastaví intro audio okamžite
		if audio_player.playing:
			audio_player.stop()
		_finish_intro()
