@tool
extends EditorScript

# Script na generovanie iOS ikon z existujúceho loga

func _run():
	print("🍎 Generujem iOS ikony z assets/logo.png")
	
	# Načítaj logo
	var logo_texture = load("res://assets/logo.png") as Texture2D
	if not logo_texture:
		print("❌ Nemožno načítať assets/logo.png")
		return
	
	# Vytvor adres<PERSON>r pre ikony ak neexistuje
	var dir = DirAccess.open("res://")
	if not dir.dir_exists("builds/ios/icons"):
		dir.make_dir_recursive("builds/ios/icons")
	
	# Definuj potrebné veľkosti ikon pre iOS
	var icon_sizes = {
		"Icon-40.png": 40,
		"Icon-58.png": 58,
		"Icon-60.png": 60,
		"Icon-76.png": 76,
		"Icon-80.png": 80,
		"Icon-87.png": 87,
		"Icon-114.png": 114,
		"Icon-120.png": 120,
		"Icon-120-1.png": 120,
		"Icon-128.png": 128,
		"Icon-136.png": 136,
		"Icon-152.png": 152,
		"Icon-167.png": 167,
		"Icon-180.png": 180,
		"Icon-192.png": 192,
		"Icon-1024.png": 1024
	}
	
	# Generuj ikony
	for icon_name in icon_sizes:
		var size = icon_sizes[icon_name]
		generate_icon(logo_texture, icon_name, size)
	
	print("✅ iOS ikony vygenerované!")

func generate_icon(source_texture: Texture2D, filename: String, size: int):
	"""Vygeneruje ikonu v danej veľkosti"""
	
	# Vytvor nový obrázok
	var image = Image.create(size, size, false, Image.FORMAT_RGBA8)
	
	# Získaj source image
	var source_image = source_texture.get_image()
	
	# Zmeň veľkosť source image na požadovanú veľkosť
	source_image.resize(size, size, Image.INTERPOLATE_LANCZOS)
	
	# Skopíruj do nového obrázka
	image.blit_rect(source_image, Rect2i(0, 0, size, size), Vector2i(0, 0))
	
	# Ulož ikonu
	var path = "res://builds/ios/icons/" + filename
	var error = image.save_png(path)
	
	if error == OK:
		print("✅ Vygenerovaná ikona: ", filename, " (", size, "x", size, ")")
	else:
		print("❌ Chyba pri generovaní ikony: ", filename, " - Error: ", error)
