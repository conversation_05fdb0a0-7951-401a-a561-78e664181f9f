# 📱 iOS KOMPATIBILITA - OPRAVA OGV VIDEÍ

## 🚨 **Problém:**
Pri exporte na iOS sa vyskytovali chyby s OGV video súbormi:
```
ERROR: Can't open file from path 'res://.godot/imported/1.ogv-c1d1e1f1g1h1i1j1k1l1m1n1o1p1q1r1.oggtheora'.
ERROR: Can't open file from path 'res://.godot/imported/2.ogv-c2d2e2f2g2h2i2j2k2l2m2n2o2p2q2r2.oggtheora'.
...
```

**Príčina:** iOS nepodporuje OGV (Ogg Theora) video formát dobre.

## ✅ **Riešenie:**

### 1. **Upravený ChapterIntro.gd**
Pridaná iOS detekcia a fallback na statické obrázky:

```gdscript
# iOS kompatibilita - použiť statické obrázky namiesto videí
if OS.get_name() == "iOS":
    print("📱 iOS detekované - používam statické obrázky namiesto videí")
    video_player.visible = false
    if background_image:
        background_image.modulate.a = 1.0
    
    # Pre kapitolu 1 - prehrať blesk sekundu po spustení
    if chapter_number == 1:
        _schedule_lightning_sound()
    
    # Simulovať trvanie videa (5 sekúnd)
    await get_tree().create_timer(5.0).timeout
    _on_video_finished()
else:
    # Načíta a spustí video pre ostatné platformy
    var video_stream = load(data.video)
    # ... pôvodný kód pre videá
```

### 2. **Aktualizovaný export_presets.cfg**
Pridaný exclude filter pre iOS export:

```ini
[preset.1] # iOS
export_filter="all_resources"
include_filter=""
exclude_filter="*.ogv"  # Vylúči všetky OGV súbory z iOS exportu
```

### 3. **Ovplyvnené súbory:**
- `assets/Kapitoly_videa/1.ogv` - `assets/Kapitoly_videa/7.ogv`
- `assets/Alpha/blesk.ogv`

## 🎮 **Funkčnosť na iOS:**

### ✅ **Čo funguje:**
- **Statické obrázky:** Namiesto videí sa zobrazujú background obrázky kapitol
- **Audio intro:** Kapitoly_uvody_zvuky stále fungujú normálne
- **Blesk efekt:** V kapitole 1 sa stále prehrá blesk zvuk a animácia
- **Timing:** 5-sekundové trvanie intro je zachované
- **Prechody:** Smooth fade efekty medzi intro a kapitolou

### 📱 **iOS špecifiká:**
- **Detekcia platformy:** `OS.get_name() == "iOS"`
- **Fallback mechanizmus:** Automaticky prepne na statické obrázky
- **Zachovaná funkcionalita:** Všetky ostatné features fungujú normálne
- **Optimalizácia:** Menší export size bez OGV súborov

## 🔄 **Alternatívne riešenia (budúcnosť):**

### 1. **Konverzia na MP4:**
```bash
# Konvertovať OGV na MP4 pomocou FFmpeg
ffmpeg -i input.ogv -c:v libx264 -c:a aac output.mp4
```

### 2. **Použitie WebM:**
- WebM je lepšie podporovaný na mobilných zariadeniach
- Menšie súbory ako MP4
- Dobrá kvalita kompresie

### 3. **Adaptívne videá:**
```gdscript
# Rôzne formáty pre rôzne platformy
var video_extensions = {
    "iOS": ".mp4",
    "Android": ".webm", 
    "Windows": ".ogv",
    "macOS": ".mp4"
}
```

## 📋 **Testovanie:**

### ✅ **Otestované na:**
- **iOS export:** Bez chýb, používa statické obrázky
- **Android export:** Stále používa OGV videá
- **Desktop export:** Stále používa OGV videá

### 🎯 **Výsledok:**
- **iOS build:** Úspešný export bez video chýb
- **Gameplay:** Plne funkčný na všetkých platformách
- **User experience:** Konzistentný napriek rozdielnym médiám

## 🚀 **Nasadenie:**

1. **Export na iOS:** Automaticky vylúči OGV súbory
2. **Spustenie hry:** Detekuje iOS a použije statické obrázky
3. **Kapitoly intro:** Fungujú s audio + obrázkami namiesto videí
4. **Žiadne chyby:** Čistý export bez video problémov

**iOS kompatibilita je teraz plne vyriešená!** 📱✅
