# 🔧 iOS MetalFX Framework - Oprava chyby

## 🚨 **Problém:**
```
Framework 'MetalFX' not found
```

**Príčina:** MetalFX je Apple framework dostupný až od iOS 16.0+, ale projekt mal nastavené `min_ios_version="14.0"`.

## ✅ **Riešenie:**

### 1. **Zvýšená minimálna iOS verzia**
V `export_presets.cfg`:

```ini
# Pred opravou:
application/min_ios_version="14.0"

# Po oprave:
application/min_ios_version="16.0"
```

### 2. **Optimalizované rendering nastavenia**
V `project.godot`:

```ini
[rendering]
textures/canvas_textures/default_texture_filter=0
renderer/rendering_method="mobile"
textures/vram_compression/import_etc2_astc=true
driver/threads/thread_model=2
```

## 📱 **Kompatibilita:**

### ✅ **iOS 16.0+ (Odporúčané)**
- **MetalFX:** Plne podporovaný
- **Výkon:** Najle<PERSON><PERSON><PERSON> m<PERSON>
- **Funkcie:** Všetky moderné iOS funkcie
- **Zariadenia:** iPhone 8 a novšie, iPad (6. generácia) a novšie

### 📊 **Pokrytie zariadení iOS 16.0+:**
- **iPhone:** iPhone 8, iPhone X, iPhone 11, iPhone 12, iPhone 13, iPhone 14, iPhone 15
- **iPad:** iPad (6. gen), iPad Air (3. gen), iPad Pro (všetky), iPad mini (5. gen)
- **Podiel:** ~85% aktívnych iOS zariadení (2024)

## 🔄 **Alternatívne riešenia:**

### 1. **Podpora starších iOS verzií (iOS 14.0+)**
Ak potrebujete podporovať staršie zariadenia:

```ini
# V export_presets.cfg
application/min_ios_version="14.0"

# V project.godot - vypnúť pokročilé rendering funkcie
[rendering]
renderer/rendering_method="mobile"
rendering/renderer/rendering_method.mobile="gl_compatibility"
```

### 2. **Podmienečné použitie MetalFX**
```gdscript
# V kóde - kontrola dostupnosti
if OS.get_name() == "iOS":
    var ios_version = OS.get_version()
    if ios_version >= "16.0":
        # Použiť MetalFX funkcie
        pass
    else:
        # Fallback pre staršie verzie
        pass
```

### 3. **Dual build stratégia**
Vytvorte dva export presety:
- **iOS Modern** - iOS 16.0+ s MetalFX
- **iOS Legacy** - iOS 14.0+ bez MetalFX

## 🎮 **Výkon a optimalizácia:**

### 📈 **iOS 16.0+ výhody:**
- **MetalFX:** Pokročilé grafické efekty
- **Metal 3:** Lepší výkon GPU
- **Unified Memory:** Efektívnejšie využitie pamäte
- **Background App Refresh:** Lepšie správanie na pozadí

### ⚡ **Mobile rendering optimalizácie:**
```ini
[rendering]
renderer/rendering_method="mobile"
textures/vram_compression/import_etc2_astc=true
driver/threads/thread_model=2
anti_aliasing/quality/msaa_2d=0
anti_aliasing/quality/msaa_3d=0
```

## 🛠️ **Riešenie problémov:**

### 1. **Ak stále máte MetalFX chyby:**
```bash
# V Xcode, skontrolujte:
# Project Settings > Deployment Target > iOS 16.0
# Build Settings > Base SDK > Latest iOS
```

### 2. **Ak potrebujete iOS 14 podporu:**
```ini
# Použite OpenGL namiesto Metal
[rendering]
rendering/renderer/rendering_method.mobile="gl_compatibility"
```

### 3. **Pre testovanie na starších zariadeniach:**
```ini
# Dočasne znížte verziu len pre testovanie
application/min_ios_version="14.0"
# A vypnite MetalFX v project settings
```

## 📋 **Kontrolný zoznam:**

### ✅ **Pre iOS 16.0+ build:**
- [ ] `min_ios_version="16.0"` v export_presets.cfg
- [ ] `renderer/rendering_method="mobile"` v project.godot
- [ ] Xcode 14+ pre build
- [ ] iOS 16+ zariadenie/simulátor pre testovanie

### ✅ **Pre iOS 14.0+ build:**
- [ ] `min_ios_version="14.0"` v export_presets.cfg
- [ ] `gl_compatibility` renderer v project.godot
- [ ] Vypnuté pokročilé rendering funkcie
- [ ] Testovanie na starších zariadeniach

## 🚀 **Výsledok:**

**iOS export teraz funguje bez MetalFX chýb!**

- **Moderné zariadenia:** Plná podpora s iOS 16.0+
- **Výkon:** Optimalizovaný pre mobile
- **Kompatibilita:** Široké pokrytie zariadení
- **Stabilita:** Bez framework chýb

**Odporúčanie:** Zostať pri iOS 16.0+ pre najlepší výkon a najnovšie funkcie. 📱✅
